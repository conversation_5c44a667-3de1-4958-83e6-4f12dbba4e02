import json

import requests

group_id = "1924518838615937292"
api_key = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************.ufzgeIwdiC7ZoVzH_d_zGD1CqT67NOrmZ_U_pWvlHxK7LpBN8R6W1e9323g6U7zPJCgPu7qHetmHuw89rrliECZAYtuNmWWHYlRv_d0s1TU5-4ycgx9pEqsrBvpRoqoSDEBHbAvposCGVfbSbW0uQgOH1pWyBmNsWgtQB11bemqKgfS_in4oewIjYgYx5hziPQsuw1qO7jJ71qWhS-SLHx9D6XIeViQu1StDYk0B0MRq1wdKvndftL-vpgSNAUezSN2ZJwbpOmR6So2HxpzuDEKEc62CU58hxz70IVB9inAT4xo_zzPYRLSZXj2LNWtQ62zKbdz_7VecDlp6JH8enw"
url = f'https://api.minimaxi.com/v1/files/upload?GroupId={group_id}'
headers1 = {
    'authority': 'api.minimaxi.com',
    'Authorization': f'Bearer {api_key}'
}

data = {
    'purpose': 'voice_clone'
}

files = {
    'file': open('1.mp3', 'rb')
}
response = requests.post(url, headers=headers1, data=data, files=files)
print(response.json())
file_id = response.json().get("file").get("file_id")
print(file_id)



#音频复刻
url = f'https://api.minimaxi.com/v1/voice_clone?GroupId={group_id}'
payload2 = json.dumps({
  "file_id": file_id,
  "voice_id": "test1234"
})
headers2 = {
  'Authorization': f'Bearer {api_key}',
  'content-type': 'application/json'
}
response = requests.request("POST", url, headers=headers2, data=payload2)
print(response.text)